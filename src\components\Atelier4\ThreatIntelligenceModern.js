import React, { useState, useEffect, useRef } from 'react';
import { AlertCircle, AlertTriangle, Target, Database, Eye, Zap, CheckCircle, Shield, Save, Bot, Globe, ChevronLeft, ChevronRight, ArrowRight, Loader2, X } from 'lucide-react';
import { useAnalysis } from '../../context/AnalysisContext';
import { api } from '../../api/apiClient';
import ctiService from '../../services/ctiService';
import ctiResultsService from '../../services/ctiResultsService';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../utils/toastUtils';
import CTIDisplay from './CTIDisplay';




// --- REBUILT COMPONENTS ---

const Section = ({ icon, title, children, step }) => (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 sm:p-8 transition-all duration-500 ease-in-out">
        <div className="flex items-center mb-6">
            <div className="flex items-center justify-center bg-indigo-100 text-indigo-600 rounded-full h-12 w-12 mr-4">
                {icon}
            </div>
            <div>
                <p className="text-sm font-semibold text-indigo-600">ÉTAPE {step}</p>
                <h2 className="text-xl font-bold text-gray-800">{title}</h2>
            </div>
        </div>
        {children}
    </div>
);

const AttackPathCarousel = ({ attackPaths, selectedPath, onSelect, isLoading }) => {
    const scrollRef = useRef(null);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    // Listen to scroll events - MUST be before early returns
    useEffect(() => {
        const handleScroll = () => updateScrollState();
        const handleResize = () => updateScrollState();

        if (scrollRef.current) {
            scrollRef.current.addEventListener('scroll', handleScroll);
            window.addEventListener('resize', handleResize);
            updateScrollState();
        }

        return () => {
            if (scrollRef.current) {
                scrollRef.current.removeEventListener('scroll', handleScroll);
            }
            window.removeEventListener('resize', handleResize);
        };
    }, [attackPaths]);

    if (isLoading) return <div className="text-center py-8"><Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-500" /></div>;
    if (attackPaths.length === 0) return <div className="text-center py-8 text-gray-500">Aucun chemin d'attaque trouvé.</div>;

    // Calculate items per view based on screen size
    const getItemsPerView = () => {
        if (typeof window !== 'undefined') {
            if (window.innerWidth >= 1024) return 3; // lg
            if (window.innerWidth >= 768) return 2;  // md
            return 1; // sm
        }
        return 3;
    };

    const itemsPerView = getItemsPerView();
    const maxIndex = Math.max(0, attackPaths.length - itemsPerView);

    // Update scroll buttons state
    const updateScrollState = () => {
        if (scrollRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
            setCanScrollLeft(scrollLeft > 0);
            setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
        }
    };

    // Scroll functions
    const scrollLeft = () => {
        if (scrollRef.current && canScrollLeft) {
            const newIndex = Math.max(0, currentIndex - 1);
            setCurrentIndex(newIndex);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24; // space-x-6 = 24px
            scrollRef.current.scrollTo({
                left: newIndex * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    const scrollRight = () => {
        if (scrollRef.current && canScrollRight) {
            const newIndex = Math.min(maxIndex, currentIndex + 1);
            setCurrentIndex(newIndex);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24; // space-x-6 = 24px
            scrollRef.current.scrollTo({
                left: newIndex * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    // Go to specific index
    const goToIndex = (index) => {
        if (scrollRef.current) {
            setCurrentIndex(index);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24;
            scrollRef.current.scrollTo({
                left: index * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    return (
        <div className="relative">
            {/* Navigation Arrows */}
            {attackPaths.length > itemsPerView && (
                <>
                    <button
                        onClick={scrollLeft}
                        disabled={!canScrollLeft}
                        className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 ${
                            canScrollLeft
                                ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-indigo-600'
                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                    >
                        <ChevronLeft className="h-5 w-5" />
                    </button>
                    <button
                        onClick={scrollRight}
                        disabled={!canScrollRight}
                        className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 ${
                            canScrollRight
                                ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-indigo-600'
                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                    >
                        <ChevronRight className="h-5 w-5" />
                    </button>
                </>
            )}

            {/* Carousel Container */}
            <div
                ref={scrollRef}
                className="flex overflow-x-auto snap-x snap-mandatory scroll-smooth py-4 px-12 space-x-4"
                style={{ scrollbarWidth: 'none', '-ms-overflow-style': 'none' }}
            >
                {attackPaths.map(path => (
                    <div key={path.id} className="flex-shrink-0 w-full sm:w-2/3 md:w-1/2 lg:w-1/3 snap-start">
                        <div onClick={() => onSelect(path)} className={`group relative h-full flex flex-col rounded-2xl p-5 cursor-pointer border transition-all duration-300 transform hover:scale-105 ${selectedPath?.id === path.id ? 'border-indigo-400 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl shadow-indigo-100' : 'border-gray-200 bg-white hover:border-indigo-200 hover:shadow-lg'}`}>
                            {/* Selection Glow Effect */}
                            {selectedPath?.id === path.id && (
                                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-400 to-blue-400 opacity-20 blur-sm"></div>
                            )}

                            {/* Header */}
                            <div className="relative flex justify-between items-start mb-3">
                                <div className="flex items-center space-x-2">
                                    <span className="text-xs font-bold text-white bg-gradient-to-r from-indigo-500 to-blue-500 px-2.5 py-1 rounded-lg shadow-sm">
                                        {path.referenceCode || `CA${attackPaths.indexOf(path) + 1}`}
                                    </span>
                                    <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                                </div>
                                {selectedPath?.id === path.id && (
                                    <div className="bg-indigo-500 rounded-full p-1">
                                        <CheckCircle className="h-4 w-4 text-white" />
                                    </div>
                                )}
                            </div>

                            {/* Main Content */}
                            <div className="relative flex-grow">
                                <h3 className="text-base font-bold text-gray-800 mb-3 leading-tight line-clamp-2 group-hover:text-indigo-700 transition-colors">
                                    {path.objectifVise}
                                </h3>

                                {/* Compact Info Grid */}
                                <div className="space-y-2.5">
                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-red-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Source</p>
                                            <p className="text-sm text-gray-700 font-medium truncate">{path.sourceRiskName}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-orange-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Événement</p>
                                            <p className="text-sm text-gray-700 truncate">{path.dreadedEventName}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-blue-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Valeur</p>
                                            <p className="text-sm text-gray-700 truncate">{path.businessValueName}</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Stakeholders Pills */}
                                {path.stakeholders && path.stakeholders.length > 0 && (
                                    <div className="mt-3 flex flex-wrap gap-1">
                                        {path.stakeholders.slice(0, 2).map((stakeholder, index) => (
                                            <span key={stakeholder.id || index} className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                                                {stakeholder.name.length > 8 ? stakeholder.name.substring(0, 8) + '...' : stakeholder.name}
                                            </span>
                                        ))}
                                        {path.stakeholders.length > 2 && (
                                            <span className="text-xs bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full font-medium">
                                                +{path.stakeholders.length - 2}
                                            </span>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Footer */}
                            <div className="relative mt-4 pt-3 border-t border-gray-100">
                                <div className="flex items-center justify-between">
                                    <span className="text-xs text-gray-500 font-medium">
                                        {selectedPath?.id === path.id ? '✓ Sélectionné' : 'Cliquer pour sélectionner'}
                                    </span>
                                    <ArrowRight className={`h-3.5 w-3.5 transition-all duration-200 ${selectedPath?.id === path.id ? 'text-indigo-500 transform translate-x-1' : 'text-gray-400 group-hover:text-indigo-400 group-hover:transform group-hover:translate-x-1'}`} />
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Pagination Dots */}
            {attackPaths.length > itemsPerView && (
                <div className="flex justify-center mt-4 space-x-2">
                    {Array.from({ length: maxIndex + 1 }, (_, index) => (
                        <button
                            key={index}
                            onClick={() => goToIndex(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                index === currentIndex
                                    ? 'bg-indigo-500 w-6'
                                    : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

const AssetSelector = ({ assets, onToggle, isLoading }) => {
    if (isLoading) return <div className="text-center py-8"><Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-500" /></div>;
    if (assets.length === 0) return (
        <div className="text-center py-12">
            <Database className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Aucun bien support pour ce chemin d'attaque</p>
        </div>
    );

    const selectedCount = assets.filter(a => a.selected).length;

    return (
        <div className="space-y-6">
            {/* Simple Header */}
            <div className="flex items-center justify-between">
                <p className="text-gray-600">
                    Sélectionnez les biens supports à analyser ({selectedCount}/{assets.length})
                </p>
                <div className="flex gap-2">
                    <button
                        onClick={() => assets.forEach(asset => !asset.selected && onToggle(asset.id))}
                        className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                    >
                        Tout sélectionner
                    </button>
                    <span className="text-gray-300">|</span>
                    <button
                        onClick={() => assets.forEach(asset => asset.selected && onToggle(asset.id))}
                        className="text-sm text-gray-600 hover:text-gray-800 font-medium"
                    >
                        Tout désélectionner
                    </button>
                </div>
            </div>

            {/* Clean List Layout */}
            <div className="space-y-3">
                {assets.map(asset => (
                    <div
                        key={asset.id}
                        onClick={() => onToggle(asset.id)}
                        className={`flex items-center justify-between p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${
                            asset.selected
                                ? 'border-indigo-300 bg-indigo-50'
                                : 'border-gray-200 bg-white hover:border-gray-300'
                        }`}
                    >
                        <div className="flex items-center space-x-4">
                            {/* Checkbox */}
                            <div className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                                asset.selected
                                    ? 'bg-indigo-500 border-indigo-500'
                                    : 'border-gray-300 hover:border-indigo-400'
                            }`}>
                                {asset.selected && <CheckCircle className="h-3 w-3 text-white" />}
                            </div>

                            {/* Asset Info */}
                            <div>
                                <h4 className="font-semibold text-gray-800">{asset.name}</h4>
                                <p className="text-sm text-gray-500">{asset.type}</p>
                            </div>
                        </div>

                        {/* Status */}
                        <div className="flex items-center space-x-2">
                            {asset.selected && (
                                <span className="text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full font-medium">
                                    Sélectionné
                                </span>
                            )}
                            <ArrowRight className={`h-4 w-4 transition-colors ${
                                asset.selected ? 'text-indigo-500' : 'text-gray-400'
                            }`} />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const AnalysisLauncher = ({ onAnalyze, disabled, isLoading, analysisType }) => {
    const buttons = [
        { type: 'nist', label: 'Vulnérabilités NIST', icon: <Shield className="mr-2 h-5 w-5" />, color: 'bg-blue-600 hover:bg-blue-700' },
        { type: 'mitre', label: 'Techniques MITRE', icon: <Target className="mr-2 h-5 w-5" />, color: 'bg-red-600 hover:bg-red-700' },
        { type: 'atlas', label: 'Menaces IA ATLAS', icon: <Bot className="mr-2 h-5 w-5" />, color: 'bg-purple-600 hover:bg-purple-700' },
    ];

    return (
        <div className="mt-8 pt-6 border-t border-gray-200 flex flex-col sm:flex-row items-center justify-center gap-4">
            {buttons.map(btn => (
                <button key={btn.type} onClick={() => onAnalyze(btn.type)} disabled={disabled || isLoading} className={`w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 rounded-lg text-sm font-semibold text-white transition-all duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed ${btn.color} shadow-md hover:shadow-lg transform hover:-translate-y-0.5`}>
                    {isLoading && analysisType === btn.type ? <Loader2 className="mr-2 h-5 w-5 animate-spin"/> : btn.icon}
                    {isLoading && analysisType === btn.type ? 'Analyse en cours...' : btn.label}
                </button>
            ))}
        </div>
    );
};




// --- MAIN PAGE COMPONENT ---
const ThreatIntelligenceModern = () => {
    const { currentAnalysis } = useAnalysis();

    // State Management - MUST be at the top level before any conditional returns
    const [isLoading, setIsLoading] = useState({ paths: false, assets: false, analysis: false });
    const [error, setError] = useState(null);
    const [attackPaths, setAttackPaths] = useState([]);
    const [selectedPath, setSelectedPath] = useState(null);
    const [supportAssets, setSupportAssets] = useState([]);
    const [ctiResults, setCtiResults] = useState(null);
    const [isSaving, setIsSaving] = useState(false);
    const [currentAnalysisType, setCurrentAnalysisType] = useState(null);
    const [hasSavedResults, setHasSavedResults] = useState(false);
    const [isLoadingResults, setIsLoadingResults] = useState(false);

    // Check for saved CTI results on component mount
    useEffect(() => {
        const checkSavedResults = async () => {
            if (!currentAnalysis) return;

            setIsLoadingResults(true);
            try {
                const response = await api.get(`/analyses/${currentAnalysis.id}/cti-results`);
                if (response.data && response.data.data) {
                    setHasSavedResults(true);

                    // Transform saved data to expected format
                    const savedData = response.data.data.results || response.data.data;
                    console.log('[CTI] Loaded saved results:', savedData);

                    // Check if data is in old format (techniques/vulnerabilities arrays) or new format (assets array)
                    let transformedData;
                    if (savedData.assets && Array.isArray(savedData.assets)) {
                        // New format - use as is
                        transformedData = savedData;
                    } else if (savedData.metadata && savedData.metadata.assetsData) {
                        // Old format - transform to new format
                        transformedData = {
                            ...savedData,
                            assets: savedData.metadata.assetsData,
                            analysisDate: savedData.metadata.analysisDate,
                            overallRiskScore: savedData.metadata.overallRiskScore,
                            totalVulnerabilities: savedData.vulnerabilities?.length || 0,
                            totalAttackTechniques: savedData.techniques?.length || 0,
                            dataSource: savedData.metadata.dataSource
                        };
                    } else {
                        // Fallback - create basic structure
                        transformedData = {
                            assets: [],
                            analysisDate: new Date().toISOString(),
                            overallRiskScore: 0,
                            totalVulnerabilities: 0,
                            totalAttackTechniques: 0,
                            dataSource: 'Unknown'
                        };
                    }

                    setCtiResults(transformedData);
                    console.log('[CTI] Transformed results:', transformedData);
                } else {
                    console.log('[CTI] No saved results found');
                }
            } catch (error) {
                // 404 is expected when no results exist yet
                if (error.response?.status !== 404) {
                    console.error('[CTI] Error loading results:', error.message);
                } else {
                    console.log('[CTI] No saved results found (404)');
                }
            } finally {
                setIsLoadingResults(false);
            }
        };

        checkSavedResults();
    }, [currentAnalysis]);

    // Data Fetching Effects - MUST be at the top level before any conditional returns
    useEffect(() => {
        if (!currentAnalysis) return;
        setIsLoading(s => ({ ...s, paths: true }));
        api.get(`/analyses/${currentAnalysis.id}/attack-paths`)
            .then(res => {
                // Handle different response structures like OperationalScenariosAI does
                let paths = [];
                if (res.data?.attackPaths) {
                    paths = res.data.attackPaths;
                } else if (res.data?.data?.attackPaths) {
                    paths = res.data.data.attackPaths;
                } else if (Array.isArray(res.data)) {
                    paths = res.data;
                }
                setAttackPaths(paths);
            })
            .catch((error) => {
                console.error('Error loading attack paths:', error);
                setError("Erreur de chargement des chemins d'attaque.");
            })
            .finally(() => setIsLoading(s => ({ ...s, paths: false })));
    }, [currentAnalysis]);

    useEffect(() => {
        if (!selectedPath) {
            setSupportAssets([]);
            return;
        }
        setIsLoading(s => ({ ...s, assets: true }));
        api.get(`/analyses/${currentAnalysis.id}/business-values`)
            .then(res => {
                const bvs = res.data?.data?.businessValues || [];
                const targetBv = bvs.find(bv => bv.id === selectedPath.businessValueId);
                setSupportAssets(targetBv?.supportAssets.map(a => ({ ...a, selected: true })) || []);
            })
            .catch((error) => {
                console.error('Error loading support assets:', error);
                setError("Erreur de chargement des biens supports.");
            })
            .finally(() => setIsLoading(s => ({ ...s, assets: false })));
    }, [selectedPath, currentAnalysis?.id]);

    // Early return if no analysis is selected - AFTER all hooks
    if (!currentAnalysis) {
        return (
            <div className="bg-gray-50 min-h-screen p-4 sm:p-6 lg:p-8">
                <div className="max-w-6xl mx-auto">
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h2 className="text-xl font-semibold text-gray-900 mb-2">
                                Aucune analyse sélectionnée
                            </h2>
                            <p className="text-gray-600">
                                Veuillez sélectionner une analyse pour commencer l'intelligence des menaces.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Handlers
    const handleAssetToggle = (assetId) => {
        setSupportAssets(assets => assets.map(a => a.id === assetId ? { ...a, selected: !a.selected } : a));
    };

    const handleAnalysis = async (analysisType) => {
        const assetsToAnalyze = supportAssets.filter(a => a.selected);
        if (assetsToAnalyze.length === 0) return showErrorToast("Veuillez sélectionner au moins un actif.");

        setIsLoading(s => ({ ...s, analysis: true }));
        setCurrentAnalysisType(analysisType);

        // Analysis type labels for better UX
        const analysisTypeLabels = {
            'nist': 'Vulnérabilités NIST',
            'euvd': 'Vulnérabilités EUVD',
            'mitre': 'Techniques MITRE ATT&CK',
            'atlas': 'Menaces IA ATLAS',
            'combined': 'CTI',
            'comprehensive': 'Analyse Complète'
        };

        const toastId = showLoadingToast(`Analyse ${analysisTypeLabels[analysisType]} en cours pour ${assetsToAnalyze.length} actif${assetsToAnalyze.length > 1 ? 's' : ''}...`);

        try {
            // Add attack path information to assets before analysis
            const assetsWithPathInfo = assetsToAnalyze.map(asset => ({
                ...asset,
                attackPathId: selectedPath.id,
                attackPathName: selectedPath.referenceCode || selectedPath.objectifVise,
                businessValueName: selectedPath.businessValueName
            }));

            const newResults = await ctiService.performCTIAnalysis(assetsWithPathInfo, analysisType);

            // Update results by merging with existing data
            setCtiResults(prev => {
                if (!prev) {
                    return newResults;
                }

                // Merge results intelligently
                const existingAssets = prev.assets || [];
                const newAssets = newResults.assets || [];

                // Update existing assets or add new ones
                const updatedAssets = [...existingAssets];
                newAssets.forEach(newAsset => {
                    const existingIndex = updatedAssets.findIndex(a => a.id === newAsset.id);
                    if (existingIndex >= 0) {
                        updatedAssets[existingIndex] = newAsset;
                    } else {
                        updatedAssets.push(newAsset);
                    }
                });

                return {
                    ...newResults,
                    assets: updatedAssets,
                    totalVulnerabilities: updatedAssets.reduce((sum, a) => sum + (a.vulnerabilities?.length || 0), 0),
                    totalAttackTechniques: updatedAssets.reduce((sum, a) => sum + (a.attackTechniques?.length || 0), 0)
                };
            });

            updateToast(toastId, `Analyse ${analysisTypeLabels[analysisType]} terminée avec succès ! ${newResults.totalVulnerabilities} vulnérabilités et ${newResults.totalAttackTechniques} techniques trouvées.`, 'success');
        } catch (err) {
            console.error('CTI Analysis error:', err);
            updateToast(toastId, `Erreur d'analyse: ${err.message}`, 'error');
        } finally {
            setIsLoading(s => ({ ...s, analysis: false }));
            setCurrentAnalysisType(null);
        }
    };

    const handleSave = async () => {
        if (!ctiResults) return showErrorToast("Aucun résultat à sauvegarder.");
        setIsSaving(true);

        try {
            // Extract vulnerabilities and techniques from assets for proper formatting
            const allVulnerabilities = ctiResults.assets?.flatMap(asset =>
                (asset.vulnerabilities || []).map(vuln => ({
                    ...vuln,
                    assetId: asset.id,
                    assetName: asset.name,
                    attackPathId: asset.attackPathId || asset.id,
                    attackPathName: asset.attackPathName,
                    businessValueName: asset.businessValueName
                }))
            ) || [];

            const allTechniques = ctiResults.assets?.flatMap(asset =>
                (asset.attackTechniques || []).map(tech => ({
                    ...tech,
                    assetId: asset.id,
                    assetName: asset.name,
                    attackPathId: asset.attackPathId || asset.id,
                    attackPathName: asset.attackPathName,
                    businessValueName: asset.businessValueName
                }))
            ) || [];

            // Format CTI data for saving
            const ctiDataToSave = ctiResultsService.formatCTIData(
                ctiResults.attackPaths || [],
                allVulnerabilities,
                allTechniques,
                {
                    analysisDate: ctiResults.analysisDate,
                    overallRiskScore: ctiResults.overallRiskScore,
                    totalVulnerabilities: ctiResults.totalVulnerabilities,
                    totalAttackTechniques: ctiResults.totalAttackTechniques,
                    dataSource: ctiResults.dataSource,
                    assetsData: ctiResults.assets
                }
            );

            await ctiResultsService.saveCTIResults(currentAnalysis.id, ctiDataToSave);
            showSuccessToast("Résultats sauvegardés avec succès !");
        } catch (err) {
            console.error('Save error:', err);
            showErrorToast(`Erreur lors de la sauvegarde: ${err.message}`);
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <div className="bg-gray-50 min-h-screen p-4 sm:p-6 lg:p-8">
            <div className="max-w-6xl mx-auto space-y-8">
                <header>
                    <h1 className="text-3xl font-bold text-gray-900">Intelligence des Menaces</h1>
                    <p className="text-lg text-gray-600">Analyse : <span className="font-semibold text-gray-700">{currentAnalysis.name}</span></p>
                </header>

                {error && <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert"><p>{error}</p></div>}

                {/* Loading indicator for saved results */}
                {isLoadingResults && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center">
                        <Loader2 className="h-5 w-5 animate-spin text-blue-600 mr-3" />
                        <span className="text-blue-800">Vérification des résultats sauvegardés...</span>
                    </div>
                )}

                {/* Show saved results directly if available */}
                {hasSavedResults && ctiResults && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div className="flex items-center">
                            <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                            <span className="text-green-800 font-medium">Résultats CTI précédents chargés automatiquement</span>
                        </div>
                    </div>
                )}

                {/* Step 1: Always show attack path selection */}
                <Section step={1} icon={<Target size={24} />} title="Sélectionner un Chemin d'Attaque">
                    <AttackPathCarousel attackPaths={attackPaths} selectedPath={selectedPath} onSelect={setSelectedPath} isLoading={isLoading.paths} />
                </Section>

                {/* Step 2: Show only if no saved results OR if user has selected a path */}
                {(!hasSavedResults && selectedPath) && (
                    <Section step={2} icon={<Database size={24} />} title="Choisir les Biens Supports à Analyser">
                        <AssetSelector assets={supportAssets} onToggle={handleAssetToggle} isLoading={isLoading.assets} />
                        <AnalysisLauncher onAnalyze={handleAnalysis} disabled={supportAssets.filter(a => a.selected).length === 0} isLoading={isLoading.analysis} analysisType={currentAnalysisType} />
                    </Section>
                )}

                {/* Step 3: Show if we have results (either saved or newly generated) */}
                {ctiResults && (
                    <Section step={hasSavedResults ? 2 : 3} icon={<Zap size={24} />} title="Consulter et Sauvegarder les Résultats">
                        <CTIDisplay results={ctiResults} onSave={handleSave} isSaving={isSaving} />
                    </Section>
                )}
            </div>
        </div>
    );
};

export default ThreatIntelligenceModern;

// Also export as ThreatIntelligence for backward compatibility
export { ThreatIntelligenceModern as ThreatIntelligence };