// routes/euvdRoutes.js
const express = require('express');
const axios = require('axios');
const router = express.Router();

// EUVD API base URL
const EUVD_BASE_URL = 'https://euvdservices.enisa.europa.eu/api';

// @route   GET /api/euvd/test
// @desc    Test endpoint to verify EUVD routes are working
// @access  Public (no auth required)
router.get('/test', (req, res) => {
  console.log('[EUVD Proxy] Test endpoint called - routes are working!');
  res.json({
    success: true,
    message: 'EUVD proxy routes are working',
    timestamp: new Date().toISOString(),
    baseUrl: EUVD_BASE_URL
  });
});

// @route   GET /api/euvd/search
// @desc    Proxy EUVD flexible search API
// @access  Public
router.get('/search', async (req, res) => {
  try {
    console.log('[EUVD Proxy] Flexible search request:', req.query);

    // Forward all query parameters to EUVD API
    const response = await axios.get(`${EUVD_BASE_URL}/search`, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log('[EUVD Proxy] Flexible search response:', {
      status: response.status,
      dataLength: response.data?.length || 0,
      totalElements: response.data?.totalElements || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Flexible search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/euvd/criticalvulnerabilities
// @desc    Proxy EUVD critical vulnerabilities API
// @access  Public
router.get('/criticalvulnerabilities', async (req, res) => {
  try {
    console.log('[EUVD Proxy] Critical vulnerabilities request');

    const response = await axios.get(`${EUVD_BASE_URL}/criticalvulnerabilities`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000
    });

    console.log('[EUVD Proxy] Critical vulnerabilities response:', {
      status: response.status,
      dataLength: response.data?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Critical vulnerabilities error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/euvd/exploitedvulnerabilities
// @desc    Proxy EUVD exploited vulnerabilities API
// @access  Public
router.get('/exploitedvulnerabilities', async (req, res) => {
  try {
    console.log('[EUVD Proxy] Exploited vulnerabilities request');

    const response = await axios.get(`${EUVD_BASE_URL}/exploitedvulnerabilities`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000
    });

    console.log('[EUVD Proxy] Exploited vulnerabilities response:', {
      status: response.status,
      dataLength: response.data?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Exploited vulnerabilities error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/euvd/lastvulnerabilities
// @desc    Proxy EUVD latest vulnerabilities API
// @access  Public
router.get('/lastvulnerabilities', async (req, res) => {
  try {
    console.log('[EUVD Proxy] Latest vulnerabilities request');

    const response = await axios.get(`${EUVD_BASE_URL}/lastvulnerabilities`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000
    });

    console.log('[EUVD Proxy] Latest vulnerabilities response:', {
      status: response.status,
      dataLength: response.data?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Latest vulnerabilities error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/euvd/lastvulnerabilities
// @desc    Proxy EUVD latest vulnerabilities API
// @access  Public
router.get('/lastvulnerabilities', async (req, res) => {
  try {
    console.log('[EUVD Proxy] Latest vulnerabilities request');

    const response = await axios.get(`${EUVD_BASE_URL}/lastvulnerabilities`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000
    });

    console.log('[EUVD Proxy] Latest vulnerabilities response:', {
      status: response.status,
      dataLength: response.data?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Latest vulnerabilities error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/euvd/enisaid/:id
// @desc    Proxy EUVD vulnerability by ID API
// @access  Public
router.get('/enisaid/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('[EUVD Proxy] Get vulnerability by ID:', id);

    const response = await axios.get(`${EUVD_BASE_URL}/enisaid`, {
      params: { id },
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 10000
    });

    console.log('[EUVD Proxy] Get vulnerability by ID response:', {
      status: response.status,
      id: response.data?.id || 'unknown'
    });

    res.json(response.data);
  } catch (error) {
    console.error('[EUVD Proxy] Get vulnerability by ID error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'EUVD API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

module.exports = router;
