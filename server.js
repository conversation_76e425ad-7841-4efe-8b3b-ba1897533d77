// server.js
const express = require('express');
const cors = require('cors');
const path = require('path');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const connectDB = require('./config/database');
const errorHandler = require('./middleware/errorHandler');
const frameworkDefinitionRoutes = require('./routes/frameworkDefinitionRoutes');
const controlDefinitionRoutes = require('./routes/controlDefinitionRoutes');
// Import route files
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const companyRoutes = require('./routes/companyRoutes');
const activityLogRoutes = require('./routes/activityLogRoutes');
const analysisRoutes = require('./routes/analysisRoutes');
const analysisComponentRoutes = require('./routes/analysisComponentRoutes');
const riskTreatmentRoutes = require('./routes/riskTreatmentRoutes'); // Import dedicated risk treatment routes
const aiRoutes = require('./routes/aiRoutes');
const eventSuggestionRoutes = require('./routes/eventSuggestionRoutes');
const securityControlRoutes = require('./routes/securityControlRoutes'); // Import new routes
const atelier2Routes = require('./routes/atelier2Routes'); // Import Atelier 2 routes
const atelier3Routes = require('./routes/atelier3Routes'); // Import Atelier 3 routes
const attackPathsRoutes = require('./routes/attackPathsRoutes'); // Import Attack Paths routes
const operationalScenariosRoutes = require('./routes/operationalScenariosRoutes'); // Import Operational Scenarios routes
const threatIntelligenceRoutes = require('./routes/threatIntelligenceRoutes'); // Import Threat Intelligence routes
const ctiRoutes = require('./routes/ctiRoutes'); // Import CTI routes

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Connect to MongoDB
connectDB();

// Middlewares - Increase payload limits for CTI data
app.use(express.json({ limit: '10mb' })); // Increased from default 100kb to 10mb
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

// Setup CORS
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? process.env.FRONTEND_URL || 'https://your-production-domain.com'
    : 'http://localhost:3000',
  credentials: true
}));

// Logger middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Debugging logs:
console.log('--- [server.js] Debugging aiRoutes Import ---');
console.log('[server.js] Typeof aiRoutes variable:', typeof aiRoutes);
console.log('[server.js] Is aiRoutes a router instance?', aiRoutes && typeof aiRoutes.stack !== 'undefined' ? 'Yes' : 'No');
console.log('[server.js] aiRoutes content:', aiRoutes); // Log the actual content
console.log('------------------------------------------');

// Set API Routes
// Mount public routes first (before protected routes)
app.use('/api/auth', authRoutes);
app.use('/api/euvd', euvdRoutes); // Mount EUVD proxy routes (public, no auth required)

// Mount protected routes
app.use('/api/users', userRoutes);
app.use('/api/companies', companyRoutes);
app.use('/api/logs', activityLogRoutes);
app.use('/api', analysisRoutes);
app.use('/api', analysisComponentRoutes);
app.use('/api', riskTreatmentRoutes); // Mount dedicated risk treatment routes
app.use('/api/framework-definitions', frameworkDefinitionRoutes);
app.use('/api/controls', controlDefinitionRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/event-suggestions', eventSuggestionRoutes);
app.use('/api/security-controls', securityControlRoutes); // Mount new routes
app.use('/api', atelier2Routes); // Mount Atelier 2 routes
app.use('/api', atelier3Routes); // Mount Atelier 3 routes
app.use('/api', attackPathsRoutes); // Mount Attack Paths routes
app.use('/api', operationalScenariosRoutes); // Mount Operational Scenarios routes
app.use('/api/threat-intelligence', threatIntelligenceRoutes); // Mount Threat Intelligence routes
app.use('/api/cti', ctiRoutes); // Mount CTI routes



// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static(path.join(__dirname, '../build')));

  // Any route that is not an API route will be redirected to index.html
  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '../build', 'index.html'));
  });
}

// Error handler middleware
app.use(errorHandler);

// Handle 404 - Route not found
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});